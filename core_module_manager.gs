/*
 * 檔案: core_module_manager.gs
 * 分類: core
 * 功能開關: -
 * 描述: 模組自動管理系統 - 已重構拆分
 * 依賴: [core_module_mapping.gs, core_module_manager_core.gs, core_module_file_ops.gs, core_module_safety.gs]
 * 最後更新: 2025-07-11 - 重構拆分為多個模組
 * 
 * ⚠️ 重要說明：
 * 此檔案已重構拆分為以下模組：
 * - core_module_mapping.gs: 模組檔案映射表定義
 * - core_module_manager_core.gs: 模組管理核心功能
 * - core_module_file_ops.gs: 模組檔案操作功能
 * - core_module_safety.gs: 模組安全網檢查和自動發現功能
 * 
 * 🔄 向後兼容性：
 * 所有原有函數已遷移到對應模組中，函數名稱保持不變
 * 現有調用不受影響，但建議逐步更新為直接調用對應模組
 */

// 🔄 所有函數已遷移到對應的模組檔案中
// 如需使用原有函數，請直接調用，向後兼容性已保持
