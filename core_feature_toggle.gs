/*
 * 檔案: core_feature_toggle.gs
 * 分類: core
 * 功能開關: -
 * 描述: 統一功能開關管理系統，整合簡化版和完整版功能
 * 依賴: [core_config.gs, core_module_manager.gs]
 * 最後更新: 2025-07-05
 */

// core_feature_toggle.gs
// == 統一功能開關管理系統 ==
// 🎛️ 整合簡化版和完整版功能開關，支援自動檔案管理和自動初始化
// 📊 支援試算表儀表板控制和代碼集中控制

/**
 * 🎛️ 功能開關配置
 * 優先從試算表讀取，如果讀取失敗則使用代碼中的預設值
 */
const FEATURE_TOGGLES = {
  // 🎨 圖片相關功能
  IMAGE_GENERATION: true,        // 圖片生成主功能
  IMAGE_BUTTONS: true,          // 圖片互動按鈕
  IMAGE_STORY: true,            // 配圖故事生成
  IMAGE_STORY_CONTINUATION: true, // 故事接龍
  
  // 🔊 音頻相關功能
  TEXT_TO_SPEECH: true,         // 文字轉語音
  CONVERSATIONAL_AUDIO: true,   // 對話音頻
  
  // 📝 內容處理功能
  NOTE_TAKING: true,            // 筆記記錄
  FILE_QUERY: true,             // 檔案查詢
  CONVERSATION_REVIEW: true,    // 對話回顧
  
  // 👥 群組功能
  GROUP_MEMBER_QUERY: true,     // 群組成員查詢（包含聊天追蹤）
  SMART_RESPONDER: true,        // 智能回應系統
  PIGGYBACK_SYSTEM: true,       // 搭便車主動發訊系統
  
  // 🔗 外部服務
  SOCIAL_MEDIA_POST: true,      // 社交媒體發文
  DRIVE_LINK_SHARING: true,     // Google Drive 連結分享
  KNOWLEDGE_BASE: true,         // 知識庫功能
  
  // 🛠️ 系統功能
  SYSTEM_HELP: true,            // 系統幫助
  MODEL_EXAMPLES: true,         // 模型範例
  SMART_SEARCH: true,           // 智能搜尋
  
  // 🧪 實驗性功能
  EXPERIMENTAL_FEATURES: false  // 實驗性功能總開關
};

/**
 * 🎛️ 檢查功能是否啟用（統一版：支援自動檔案管理和自動初始化）
 * @param {string} featureName - 功能名稱（對應 FEATURE_TOGGLES 中的鍵）
 * @param {boolean} autoManage - 是否自動管理檔案（預設 false，簡化模式）
 * @param {boolean} autoInit - 是否自動初始化缺失的功能開關（預設 true）
 * @returns {boolean} 功能是否啟用
 */
function isFeatureEnabled(featureName, autoManage = false, autoInit = true) {
  try {
    // 1. 優先從試算表讀取開關狀態
    const sheetValue = getConfigValue(`功能開關_${featureName}`, 'APIKEY');
    let isEnabled = null;

    if (sheetValue !== null) {
      // 支援多種格式：true/false, 1/0, 啟用/停用, enabled/disabled
      const normalizedValue = sheetValue.toString().toLowerCase();
      if (['true', '1', '啟用', 'enabled', 'on', 'yes'].includes(normalizedValue)) {
        isEnabled = true;
      } else if (['false', '0', '停用', 'disabled', 'off', 'no'].includes(normalizedValue)) {
        isEnabled = false;
      }
    } else if (autoInit) {
      // 🔧 如果試算表中沒有這個設定項，自動初始化
      console.log(`🔧 自動初始化功能開關: ${featureName}`);
      initializeFeatureToggle(featureName);
    }

    // 2. 備用：使用代碼中的預設值
    if (isEnabled === null) {
      const defaultValue = FEATURE_TOGGLES[featureName];
      if (defaultValue !== undefined) {
        isEnabled = defaultValue;
      } else {
        // 3. 最終備用：預設啟用
        console.warn(`⚠️ 未知的功能開關: ${featureName}，預設啟用`);
        isEnabled = true;
      }
    }

    // 🔄 自動檔案管理（如果啟用）
    if (autoManage && typeof autoManageModules === 'function') {
      try {
        // 只管理當前功能的檔案
        const moduleInfo = MODULE_FILE_MAPPING[featureName];
        if (moduleInfo) {
          if (isEnabled) {
            activateModule(featureName, moduleInfo);
          } else {
            deactivateModule(featureName, moduleInfo);
          }
        }
      } catch (autoError) {
        console.warn(`⚠️ 自動檔案管理失敗: ${featureName}`, autoError);
      }
    }

    return isEnabled;

  } catch (error) {
    console.error(`❌ 檢查功能開關失敗: ${featureName}`, error);
    // 錯誤時預設啟用，確保系統正常運作
    return true;
  }
}

/**
 * 🔧 自動初始化功能開關
 * 當檢測到缺少功能開關時，自動在 APIKEY 工作表中建立並設定為 plain text 的 true
 */
function initializeFeatureToggle(featureName) {
  try {
    console.log(`🔧 自動初始化功能開關: 功能開關_${featureName}`);

    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = ss.getSheetByName('APIKEY');
    if (!sheet) {
      console.error('找不到 APIKEY 工作表');
      return false;
    }

    // 找到最後一行
    const lastRow = sheet.getLastRow();
    const newRow = lastRow + 1;

    // 在 A 欄添加標題
    const titleCell = sheet.getRange(newRow, 1);
    titleCell.setValue(`功能開關_${featureName}`);

    // 在 B 欄添加值，設定為 plain text 格式的 true
    const valueCell = sheet.getRange(newRow, 2);
    valueCell.setValue('true');
    valueCell.setNumberFormat('@'); // 設定為 plain text 格式

    console.log(`✅ 已自動建立功能開關: 功能開關_${featureName} = true (plain text)`);
    return true;

  } catch (error) {
    console.error(`❌ 自動初始化功能開關失敗: ${featureName}`, error);
    return false;
  }
}

/**
 * 🔧 批量初始化所有功能開關
 */
function initializeAllFeatureToggles() {
  console.log('🔧 開始批量初始化功能開關...');
  
  const results = {
    initialized: [],
    existing: [],
    failed: []
  };
  
  for (const featureName in FEATURE_TOGGLES) {
    try {
      const sheetValue = getConfigValue(`功能開關_${featureName}`, 'APIKEY');
      if (sheetValue !== null) {
        console.log(`  ✅ 功能開關_${featureName}: 已存在`);
        results.existing.push(featureName);
      } else {
        const success = initializeFeatureToggle(featureName);
        if (success) {
          results.initialized.push(featureName);
        } else {
          results.failed.push(featureName);
        }
      }
    } catch (error) {
      console.error(`  ❌ 處理 ${featureName} 時發生錯誤:`, error);
      results.failed.push(featureName);
    }
  }
  
  console.log('🎯 批量初始化完成:');
  console.log(`  新建立: ${results.initialized.length} 個`);
  console.log(`  已存在: ${results.existing.length} 個`);
  console.log(`  失敗: ${results.failed.length} 個`);

  if (results.initialized.length > 0) {
    console.log('📋 新建立的功能開關:', results.initialized);
  }

  if (results.failed.length > 0) {
    console.log('❌ 失敗的功能開關:', results.failed);
  }

  return results;
}

// ===== 🔄 向後兼容函數 =====

/**
 * 🎛️ 簡化版功能開關檢查（向後兼容）
 * 等同於 isFeatureEnabled(featureName, false, true)
 */
function isSimpleFeatureEnabled(featureName) {
  return isFeatureEnabled(featureName, false, true);
}

/**
 * 🔄 智能功能管理：檢查開關並自動管理檔案
 * 這是主要的便捷函數，會同時檢查開關狀態和管理檔案
 */
function smartFeatureCheck(featureName) {
  return isFeatureEnabled(featureName, true, true); // 啟用自動檔案管理和自動初始化
}

// ===== 📋 功能檢查入口函數 =====

/**
 * 🎨 圖片功能統一入口檢查
 */
function checkImageFeatureEnabled() {
  if (!isFeatureEnabled('IMAGE_GENERATION', false)) {
    return {
      enabled: false,
      message: '🎨 圖片生成功能目前已停用\n\n💡 如需使用此功能，請在試算表設定 功能開關_IMAGE_GENERATION = true'
    };
  }
  return { enabled: true };
}

/**
 * 🎨 簡化的圖片功能檢查（向後兼容）
 */
function checkSimpleImageFeature() {
  return checkImageFeatureEnabled();
}

/**
 * 🔊 音頻功能統一入口檢查
 */
function checkAudioFeatureEnabled(audioType = 'TEXT_TO_SPEECH') {
  // 🔄 自動同步檔案狀態
  autoSyncTrigger();

  if (!isFeatureEnabled(audioType, false)) { // 避免重複觸發
    return {
      enabled: false,
      message: '🔊 音頻功能目前已停用\n\n💡 如需使用此功能，請在試算表設定相應的功能開關'
    };
  }
  return { enabled: true };
}

/**
 * 🔊 簡化的音頻功能檢查（向後兼容）
 */
function checkSimpleAudioFeature(audioType = 'TEXT_TO_SPEECH') {
  return checkAudioFeatureEnabled(audioType);
}

/**
 * 📝 內容處理功能統一入口檢查
 */
function checkContentFeatureEnabled(contentType) {
  if (!isFeatureEnabled(contentType)) {
    return {
      enabled: false,
      message: '📝 此功能目前已停用\n\n💡 如需使用此功能，請聯繫管理員啟用'
    };
  }
  return { enabled: true };
}

/**
 * 📝 簡化的內容功能檢查（向後兼容）
 */
function checkSimpleContentFeature(contentType) {
  return checkContentFeatureEnabled(contentType);
}

// ===== 🎛️ 管理和報告函數 =====

/**
 * 🎛️ 獲取所有功能開關狀態（用於除錯和管理）
 */
function getAllFeatureStates() {
  const states = {};
  for (const featureName in FEATURE_TOGGLES) {
    states[featureName] = isFeatureEnabled(featureName, false, false); // 不觸發自動管理和初始化
  }
  return states;
}

/**
 * 🎛️ 生成功能開關狀態報告
 */
function generateFeatureToggleReport() {
  const states = getAllFeatureStates();
  let report = '🎛️ 功能開關狀態報告\n\n';

  // 按類別分組顯示
  const categories = {
    '🎨 圖片功能': ['IMAGE_GENERATION', 'IMAGE_BUTTONS', 'IMAGE_STORY', 'IMAGE_STORY_CONTINUATION'],
    '🔊 音頻功能': ['TEXT_TO_SPEECH', 'CONVERSATIONAL_AUDIO'],
    '📝 內容處理': ['NOTE_TAKING', 'FILE_QUERY', 'CONVERSATION_REVIEW'],
    '👥 群組功能': ['GROUP_MEMBER_QUERY', 'GROUP_CHAT_TRACKING'],
    '🔗 外部服務': ['SOCIAL_MEDIA_POST', 'DRIVE_LINK_SHARING', 'KNOWLEDGE_BASE'],
    '🛠️ 系統功能': ['SYSTEM_HELP', 'MODEL_EXAMPLES', 'SMART_SEARCH'],
    '🧪 實驗功能': ['EXPERIMENTAL_FEATURES']
  };

  for (const [category, features] of Object.entries(categories)) {
    report += `${category}:\n`;
    for (const feature of features) {
      const status = states[feature] ? '✅ 啟用' : '❌ 停用';
      report += `  • ${feature}: ${status}\n`;
    }
    report += '\n';
  }

  return report;
}

/**
 * 📊 生成簡化功能狀態報告（向後兼容）
 */
function generateSimpleFeatureReport() {
  const report = {
    timestamp: new Date().toISOString(),
    features: {}
  };

  for (const featureName in FEATURE_TOGGLES) {
    report.features[featureName] = {
      enabled: isFeatureEnabled(featureName, false, false),
      source: getConfigValue(`功能開關_${featureName}`, 'APIKEY') !== null ? 'spreadsheet' : 'code'
    };
  }

  console.log('📊 簡化功能狀態報告:', JSON.stringify(report, null, 2));
  return report;
}

/**
 * 🎛️ 設定功能開關（程式化控制）
 * 注意：這只會修改記憶體中的值，不會寫入試算表
 */
function setFeatureToggle(featureName, enabled) {
  if (FEATURE_TOGGLES.hasOwnProperty(featureName)) {
    FEATURE_TOGGLES[featureName] = enabled;
    console.log(`🎛️ 功能開關已更新: ${featureName} = ${enabled}`);
    return true;
  } else {
    console.error(`❌ 未知的功能開關: ${featureName}`);
    return false;
  }
}

// ===== 🔄 自動同步功能 =====

/**
 * 🎛️ 自動同步觸發器
 * 當任何功能開關被檢查時，自動檢查是否需要同步檔案
 */
function autoSyncTrigger() {
  try {
    // 檢查是否有功能開關變更需要同步
    const needsSync = checkIfSyncNeeded();

    if (needsSync) {
      console.log('🔄 檢測到功能開關變更，自動執行同步...');
      const syncResult = syncAllModules();
      console.log('✅ 自動同步完成:', syncResult);
      return syncResult;
    } else {
      console.log('✅ 檔案狀態已同步，無需操作');
      return { message: '已同步' };
    }

  } catch (error) {
    console.error('❌ 自動同步失敗:', error);
    return { error: error.message };
  }
}

/**
 * 🔍 檢查是否需要同步
 * 比較試算表開關狀態與實際檔案狀態
 */
function checkIfSyncNeeded() {
  try {
    // 檢查 MODULE_FILE_MAPPING 是否存在（來自 core_module_manager.gs）
    if (typeof MODULE_FILE_MAPPING === 'undefined') {
      console.warn('⚠️ MODULE_FILE_MAPPING 未定義，跳過同步檢查');
      return false;
    }

    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      const isEnabled = isFeatureEnabled(featureName, false, false); // 不觸發自動管理和初始化

      // 檢查檔案狀態是否與開關狀態一致
      for (const fileName of moduleInfo.files) {
        const inRoot = checkFileExists(fileName, 'root');
        const inSleeping = checkFileExists(fileName, '_sleeping');

        if (isEnabled && !inRoot && inSleeping) {
          // 功能啟用但檔案在 sleeping，需要同步
          return true;
        }

        if (!isEnabled && inRoot && !inSleeping) {
          // 功能停用但檔案在根目錄，需要同步
          return true;
        }
      }
    }

    return false; // 不需要同步

  } catch (error) {
    console.error('檢查同步需求失敗:', error);
    return true; // 錯誤時假設需要同步
  }
}

// ===== 🛡️ 功能開關安全網整合 =====

/**
 * 🛡️ 檢查功能開關與映射表的一致性
 * 確保 FEATURE_TOGGLES 與 MODULE_FILE_MAPPING 保持同步
 */
function validateFeatureToggleConsistency() {
  console.log('🛡️ 檢查功能開關與映射表一致性...');

  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFeatureToggles: 0,
      totalMappedFeatures: 0,
      missingInMapping: 0,
      missingInToggles: 0,
      consistent: 0
    },
    issues: {
      missingInMapping: [],
      missingInToggles: [],
      suggestions: []
    }
  };

  try {
    // 檢查 FEATURE_TOGGLES 中的功能是否都有對應的映射
    for (const featureName in FEATURE_TOGGLES) {
      report.summary.totalFeatureToggles++;

      if (typeof MODULE_FILE_MAPPING !== 'undefined' && MODULE_FILE_MAPPING[featureName]) {
        report.summary.consistent++;
      } else {
        report.summary.missingInMapping++;
        report.issues.missingInMapping.push({
          feature: featureName,
          suggestion: `考慮在 MODULE_FILE_MAPPING 中添加 ${featureName} 的檔案映射，或者這是一個不需要檔案的功能開關`
        });
      }
    }

    // 檢查 MODULE_FILE_MAPPING 中的功能是否都有對應的開關
    if (typeof MODULE_FILE_MAPPING !== 'undefined') {
      for (const featureName in MODULE_FILE_MAPPING) {
        report.summary.totalMappedFeatures++;

        if (!FEATURE_TOGGLES.hasOwnProperty(featureName)) {
          report.summary.missingInToggles++;
          report.issues.missingInToggles.push({
            feature: featureName,
            suggestion: `在 FEATURE_TOGGLES 中添加 ${featureName}: true`
          });
        }
      }
    }

    // 生成建議
    if (report.summary.missingInToggles > 0) {
      report.issues.suggestions.push({
        priority: 'high',
        action: 'add_missing_toggles',
        description: `有 ${report.summary.missingInToggles} 個映射功能缺少功能開關`,
        autoFixAvailable: true
      });
    }

    if (report.summary.missingInMapping > 0) {
      report.issues.suggestions.push({
        priority: 'medium',
        action: 'review_unmapped_toggles',
        description: `有 ${report.summary.missingInMapping} 個功能開關沒有檔案映射`,
        note: '這些可能是不需要檔案的功能開關（如系統級開關）'
      });
    }

    console.log('✅ 功能開關一致性檢查完成');
    console.log(`📊 功能開關: ${report.summary.totalFeatureToggles}, 映射功能: ${report.summary.totalMappedFeatures}`);
    console.log(`🔗 一致: ${report.summary.consistent}, 缺少映射: ${report.summary.missingInMapping}, 缺少開關: ${report.summary.missingInToggles}`);

    return report;

  } catch (error) {
    console.error('❌ 功能開關一致性檢查失敗:', error);
    report.issues.suggestions.push({
      priority: 'critical',
      action: 'fix_system_error',
      description: `檢查過程中發生錯誤: ${error.message}`
    });
    return report;
  }
}

/**
 * 🔧 自動修復功能開關一致性問題
 */
function autoFixFeatureToggleConsistency(confirmFix = false) {
  if (!confirmFix) {
    console.log('⚠️ 自動修復需要確認，請設定 confirmFix = true');
    const preview = generateFeatureToggleFixPreview();
    return {
      success: false,
      message: '需要確認才能執行自動修復',
      previewChanges: preview
    };
  }

  console.log('🔧 開始修復功能開關一致性...');

  const fixes = [];

  // 檢查並添加缺失的功能開關
  if (typeof MODULE_FILE_MAPPING !== 'undefined') {
    for (const featureName in MODULE_FILE_MAPPING) {
      if (!FEATURE_TOGGLES.hasOwnProperty(featureName)) {
        // 注意：這只會修改記憶體中的值，不會修改源代碼
        FEATURE_TOGGLES[featureName] = true;
        fixes.push({
          action: 'added_toggle',
          feature: featureName,
          value: true,
          note: '已添加到記憶體中，需要手動更新源代碼'
        });
        console.log(`🔧 已添加功能開關: ${featureName} = true`);
      }
    }
  }

  return {
    success: true,
    message: `已修復 ${fixes.length} 個一致性問題`,
    fixes: fixes,
    note: '記憶體中的修改在重新載入後會失效，請手動更新 FEATURE_TOGGLES 常數'
  };
}

/**
 * 📋 生成功能開關修復預覽
 */
function generateFeatureToggleFixPreview() {
  const preview = [];

  if (typeof MODULE_FILE_MAPPING !== 'undefined') {
    for (const featureName in MODULE_FILE_MAPPING) {
      if (!FEATURE_TOGGLES.hasOwnProperty(featureName)) {
        preview.push({
          action: `添加功能開關 ${featureName}`,
          code: `// 在 FEATURE_TOGGLES 中添加:\n${featureName}: true,  // ${MODULE_FILE_MAPPING[featureName].description || '自動添加的功能開關'}`
        });
      }
    }
  }

  return preview;
}

/**
 * 🎛️ 一鍵同步所有模組
 * 根據試算表設定同步所有功能模組的檔案狀態
 */
function syncAllModules() {
  try {
    console.log('🔄 開始一鍵同步所有模組...');

    // 調用 core_module_manager.gs 中的自動管理函數
    if (typeof autoManageModules === 'function') {
      const results = autoManageModules();
      console.log('✅ 一鍵同步完成:', results);
      return results;
    } else {
      console.error('❌ core_module_manager.gs 未載入或 autoManageModules 函數不存在');
      return { error: 'ModuleManager 不可用' };
    }

  } catch (error) {
    console.error('❌ 一鍵同步失敗:', error);
    return { error: error.message };
  }
}

// ===== 🧪 測試函數 =====

/**
 * 🧪 測試功能開關系統（統一版）
 */
function testFeatureToggleSystem() {
  console.log('🧪 測試統一功能開關系統...');

  // 1. 先執行批量初始化
  console.log('🔧 步驟1: 批量初始化功能開關');
  const initResults = initializeAllFeatureToggles();

  // 2. 測試圖片功能
  console.log('🎨 步驟2: 測試圖片功能');
  const imageCheck = checkImageFeatureEnabled();
  console.log('圖片功能檢查:', imageCheck);

  // 3. 測試音頻功能
  console.log('🔊 步驟3: 測試音頻功能');
  const audioCheck = checkAudioFeatureEnabled('TEXT_TO_SPEECH');
  console.log('音頻功能檢查:', audioCheck);

  // 4. 生成報告
  console.log('📊 步驟4: 生成功能狀態報告');
  const report = generateFeatureToggleReport();

  return {
    initialization: initResults,
    imageFeature: imageCheck,
    audioFeature: audioCheck,
    fullReport: report
  };
}

/**
 * 🧪 測試簡化功能開關（向後兼容）
 */
function testSimpleFeatureToggle() {
  return testFeatureToggleSystem();
}

/**
 * 🔧 一鍵設定功能開關系統
 * 執行完整的功能開關初始化和測試
 */
function setupFeatureToggleSystem() {
  console.log('🚀 開始一鍵設定功能開關系統...');

  try {
    // 1. 批量初始化
    const initResults = initializeAllFeatureToggles();

    // 2. 驗證初始化結果
    if (initResults.failed.length > 0) {
      console.warn('⚠️ 部分功能開關初始化失敗，但系統仍可正常運作');
    }

    // 3. 生成最終報告
    const finalReport = generateFeatureToggleReport();

    console.log('✅ 功能開關系統設定完成！');
    console.log('💡 您現在可以在 APIKEY 工作表中看到所有功能開關');
    console.log('🎛️ 將任何功能開關設定為 false 即可停用該功能');

    return {
      success: true,
      initialization: initResults,
      finalReport: finalReport
    };

  } catch (error) {
    console.error('❌ 功能開關系統設定失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🧪 測試自動同步功能
 * 用於驗證試算表設定後是否自動同步檔案
 */
function testAutoSync() {
  console.log('🧪 測試自動同步功能...');

  // 1. 檢查試算表讀取
  console.log('📊 檢查試算表讀取:');
  const sheetValue = getConfigValue('功能開關_IMAGE_GENERATION', 'APIKEY');
  console.log('試算表值:', sheetValue);

  // 2. 檢查功能開關狀態
  console.log('🎛️ 檢查功能開關狀態:');
  const isEnabled = isFeatureEnabled('IMAGE_GENERATION', false, false); // 不觸發自動管理和初始化
  console.log('功能開關狀態:', isEnabled);

  // 3. 檢查當前檔案狀態（如果 MODULE_FILE_MAPPING 存在）
  if (typeof MODULE_FILE_MAPPING !== 'undefined') {
    console.log('📁 檢查當前檔案狀態:');
    const moduleInfo = MODULE_FILE_MAPPING['IMAGE_GENERATION'];
    if (moduleInfo) {
      const fileStatus = {};
      for (const fileName of moduleInfo.files) {
        fileStatus[fileName] = {
          inRoot: checkFileExists(fileName, 'root'),
          inSleeping: checkFileExists(fileName, '_sleeping')
        };
      }
      console.log('檔案狀態:', fileStatus);

      // 4. 觸發功能檢查（應該自動同步）
      console.log('🔄 觸發圖片功能檢查...');
      const imageCheck = checkImageFeatureEnabled();
      console.log('圖片功能檢查結果:', imageCheck);

      // 5. 檢查同步後檔案狀態
      console.log('📁 檢查同步後檔案狀態:');
      const fileStatusAfter = {};
      for (const fileName of moduleInfo.files) {
        fileStatusAfter[fileName] = {
          inRoot: checkFileExists(fileName, 'root'),
          inSleeping: checkFileExists(fileName, '_sleeping')
        };
      }
      console.log('同步後檔案狀態:', fileStatusAfter);

      return {
        sheetValue: sheetValue,
        isEnabled: isEnabled,
        imageCheck: imageCheck,
        filesBefore: fileStatus,
        filesAfter: fileStatusAfter,
        autoSyncWorked: JSON.stringify(fileStatus) !== JSON.stringify(fileStatusAfter)
      };
    }
  }

  return {
    sheetValue: sheetValue,
    isEnabled: isEnabled,
    message: 'MODULE_FILE_MAPPING 不可用，跳過檔案狀態檢查'
  };
}

/**
 * 🛡️ 綜合功能開關安全網檢查
 * 整合功能開關驗證與模組管理安全網
 */
function runFeatureToggleSafetyNet() {
  console.log('🛡️ 開始功能開關綜合安全網檢查...');

  const safetyReport = {
    timestamp: new Date().toISOString(),
    featureToggleCheck: null,
    moduleManagerCheck: null,
    overallHealth: 'unknown',
    recommendations: [],
    criticalIssues: []
  };

  try {
    // 1. 檢查功能開關一致性
    console.log('🔍 步驟1: 檢查功能開關一致性');
    safetyReport.featureToggleCheck = validateFeatureToggleConsistency();

    // 2. 調用模組管理器的安全網檢查
    console.log('🔍 步驟2: 檢查模組管理器');
    if (typeof runSafetyNetCheck === 'function') {
      safetyReport.moduleManagerCheck = runSafetyNetCheck();
    } else {
      console.warn('⚠️ 模組管理器安全網不可用');
      safetyReport.criticalIssues.push({
        type: 'missing_dependency',
        message: 'core_module_manager.gs 的安全網功能不可用',
        severity: 'medium'
      });
    }

    // 3. 整合分析結果
    console.log('🎯 步驟3: 整合分析結果');
    integrateAnalysisResults(safetyReport);

    // 4. 評估整體健康狀況
    safetyReport.overallHealth = assessFeatureToggleHealth(safetyReport);

    console.log('✅ 功能開關安全網檢查完成');
    console.log(`🏥 整體健康狀況: ${safetyReport.overallHealth}`);

    return safetyReport;

  } catch (error) {
    console.error('❌ 功能開關安全網檢查失敗:', error);
    safetyReport.criticalIssues.push({
      type: 'system_error',
      message: `安全網檢查過程中發生錯誤: ${error.message}`,
      severity: 'critical'
    });
    safetyReport.overallHealth = 'critical';
    return safetyReport;
  }
}

/**
 * 🎯 整合分析結果
 */
function integrateAnalysisResults(safetyReport) {
  const ftCheck = safetyReport.featureToggleCheck;
  const mmCheck = safetyReport.moduleManagerCheck;

  // 分析功能開關問題
  if (ftCheck && ftCheck.summary.missingInToggles > 0) {
    safetyReport.criticalIssues.push({
      type: 'missing_feature_toggles',
      count: ftCheck.summary.missingInToggles,
      message: `有 ${ftCheck.summary.missingInToggles} 個映射功能缺少功能開關`,
      severity: 'high'
    });
    safetyReport.recommendations.push({
      priority: 'high',
      action: 'fix_missing_toggles',
      description: '修復缺失的功能開關',
      autoFix: 'autoFixFeatureToggleConsistency(true)'
    });
  }

  // 分析模組管理問題
  if (mmCheck && mmCheck.validation) {
    if (mmCheck.validation.summary.missingFiles > 0) {
      safetyReport.criticalIssues.push({
        type: 'missing_module_files',
        count: mmCheck.validation.summary.missingFiles,
        message: `有 ${mmCheck.validation.summary.missingFiles} 個模組檔案缺失`,
        severity: 'high'
      });
    }
  }

  // 生成整合建議
  if (safetyReport.criticalIssues.length === 0) {
    safetyReport.recommendations.push({
      priority: 'low',
      action: 'maintain_current_state',
      description: '系統狀態良好，建議定期執行安全網檢查'
    });
  }
}

/**
 * 🏥 評估功能開關整體健康狀況
 */
function assessFeatureToggleHealth(safetyReport) {
  const criticalCount = safetyReport.criticalIssues.filter(i => i.severity === 'critical').length;
  const highCount = safetyReport.criticalIssues.filter(i => i.severity === 'high').length;
  const mediumCount = safetyReport.criticalIssues.filter(i => i.severity === 'medium').length;

  if (criticalCount > 0) {
    return 'critical';
  } else if (highCount > 0) {
    return 'poor';
  } else if (mediumCount > 1) {
    return 'fair';
  } else {
    return 'good';
  }
}

// ===== 📋 使用說明 =====
/*
🎛️ 統一功能開關使用方法：

1️⃣ 試算表控制（推薦）：
   在 APIKEY 工作表添加以下欄位：
   A24: 功能開關_IMAGE_GENERATION   B24: false (設為 Plain text 格式)
   A25: 功能開關_TEXT_TO_SPEECH     B25: true
   A26: 功能開關_NOTE_TAKING        B26: true

2️⃣ 簡化模式（預設）：
   const enabled = isFeatureEnabled('IMAGE_GENERATION'); // 不自動管理檔案
   const check = checkImageFeatureEnabled();

3️⃣ 完整模式（自動檔案管理）：
   const enabled = smartFeatureCheck('IMAGE_GENERATION'); // 自動管理檔案
   const enabled = isFeatureEnabled('IMAGE_GENERATION', true); // 手動指定

4️⃣ 自動初始化：
   - 系統會自動在試算表中建立缺失的功能開關
   - 預設值為 true（plain text 格式）

5️⃣ 向後兼容：
   - isSimpleFeatureEnabled() 仍然可用
   - checkSimpleImageFeature() 等函數仍然可用

6️⃣ 測試和設定：
   setupFeatureToggleSystem(); // 一鍵設定
   testFeatureToggleSystem();  // 完整測試
   testAutoSync();             // 測試自動同步

7️⃣ 🛡️ 安全網功能（新增）：
   runFeatureToggleSafetyNet();        // 綜合安全網檢查
   validateFeatureToggleConsistency(); // 檢查功能開關一致性
   autoFixFeatureToggleConsistency(true); // 自動修復一致性問題

✅ 優點：
- 統一的功能開關入口
- 支援簡化模式和完整模式
- 自動初始化缺失的設定
- 完整的向後兼容性
- 智能檔案管理（可選）
- 內建安全網機制

🛡️ 安全網說明：
- 自動檢查功能開關與映射表的一致性
- 發現缺失的功能開關或映射關係
- 提供自動修復建議和預覽
- 與模組管理器安全網整合
- 評估整體系統健康狀況

🔄 整合說明：
- 整合了 SimpleFeatureToggle.gs 和 FeatureToggle.gs
- 預設使用簡化模式（不自動管理檔案）
- 可選擇啟用完整模式（自動檔案管理）
- 保留所有原有函數的向後兼容性
- 內建安全網機制確保系統穩定性
*/
