/*
 * 檔案: core_main.gs
 * 分類: core
 * 功能開關: -
 * 描述: 主要控制邏輯和程式進入點
 * 依賴: [core_config.gs, FeatureToggle.gs]
 * 最後更新: 2025-07-011
 */

// ===== 版本資訊 =====
// tribe-line-bot 統一版本管理
const SCRIPT_VERSION = "v1.5.0"; // 統一版本號 - 代碼清理與重構完成

// == LINE Bot 主程式入口 ==
// 🎯 模組化架構 - 各功能已重構到各個 .gs 檔案中
// 
// 📁 專案結構：
// ├── Core.gs          - 核心配置與基礎函數
// ├── Sheets.gs        - 工作表管理
// ├── Webhook.gs       - LINE Webhook 處理
// ├── TextProcessor.gs - 文字處理與命令解析
// ├── TextProcessor_AIFirst.gs - 🤖 AI-First 文字處理
// ├── MediaProcessor.gs- 媒體檔案處理
// ├── DriveHandler.gs  - Google Drive 處理
// ├── Memory_System.gs - 記憶系統
// ├── AI_Features.gs   - AI 智能功能
// ├── File_Analysis.gs - 檔案分析
// ├── GroupChatTracker.gs - 群組聊天記錄功能
// ├── PromptManager.gs - 🆕 統一提示詞管理系統
// └── GeminiAdvanced.gs - 🆕 Gemini TTS 和圖像生成功能

// ===== 程式入口點 =====
// 所有的 LINE Webhook 請求都從這裡開始處理

/**
 * 🚀 LINE Bot 主要入口點
 * 接收來自 LINE 平台的 Webhook 請求
 * 
 * 🔄 處理流程：
 * 1. Webhook.gs - 解析和路由訊息
 * 2. TextProcessor.gs - 處理文字訊息
 * 3. MediaProcessor.gs - 處理媒體檔案
 * 4. DriveHandler.gs - 處理 Google Drive 連結
 * 5. Memory_System.gs - 記錄到記憶系統
 * 6. AI_Features.gs - AI 智能回答
 * 7. File_Analysis.gs - 檔案分析
 * 8. GroupChatTracker.gs - 🆕 群組聊天記錄與查詢
 */

// 注意：實際的 doPost 函數已移動到 modules_line_webhook.gs
// 這裡保留一個重定向函數以確保向後兼容

// ===== 系統管理函數 =====

/**
 * 🔧 系統健康檢查
 * 檢查所有模組的運作狀況
 */
// 修復 Code.gs 中的 systemHealthCheck 函數

function systemHealthCheck() {
  try {
    const healthReport = {
      timestamp: new Date(),
      modules: {},
      overall: 'healthy',
      warnings: [],
      errors: []
    };

    // 檢查各模組
    try {
      // 檢查配置模組
      const config = getConfig();
      healthReport.modules.core = config ? 'healthy' : 'error';
      if (!config) {
        healthReport.errors.push('Core.gs: 無法讀取配置');
      }
    } catch (e) {
      healthReport.modules.core = 'error';
      healthReport.errors.push('Core.gs: ' + e.message);
    }

    try {
      // 檢查工作表健康狀態
      const sheetsHealth = checkSheetsHealth();
      if (sheetsHealth.error) {
        healthReport.modules.sheets = 'error';
        healthReport.errors.push('Sheets.gs: ' + sheetsHealth.error);
      } else if (sheetsHealth.missingSheets.length > 0) {
        healthReport.modules.sheets = 'warning';
        healthReport.warnings.push(`缺少工作表: ${sheetsHealth.missingSheets.join(', ')}`);
      } else {
        healthReport.modules.sheets = 'healthy';
      }
    } catch (e) {
      healthReport.modules.sheets = 'error';
      healthReport.errors.push('Sheets.gs: ' + e.message);
    }

    try {
      // 檢查記憶系統健康狀態
      const memoryHealth = checkMemorySystemHealth();
      if (memoryHealth.error) {
        healthReport.modules.memory = 'error';
        healthReport.errors.push('Memory_System.gs: ' + memoryHealth.error);
      } else if (memoryHealth.status === 'warning') {
        healthReport.modules.memory = 'warning';
        healthReport.warnings.push('記憶系統工作表部分缺失');
      } else {
        healthReport.modules.memory = 'healthy';
      }
    } catch (e) {
      healthReport.modules.memory = 'error';
      healthReport.errors.push('Memory_System.gs: ' + e.message);
    }

    // 🆕 檢查群組聊天記錄系統
    try {
      const groupChatSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('群組發言記錄');
      if (groupChatSheet) {
        healthReport.modules.groupChat = 'healthy';
      } else {
        healthReport.modules.groupChat = 'warning';
        healthReport.warnings.push('群組發言記錄工作表未建立');
      }
    } catch (e) {
      healthReport.modules.groupChat = 'error';
      healthReport.errors.push('GroupChatTracker.gs: ' + e.message);
    }

    // 整體狀態評估
    if (healthReport.errors.length > 0) {
      healthReport.overall = 'error';
    } else if (healthReport.warnings.length > 0) {
      healthReport.overall = 'warning';
    }

    console.log('系統健康檢查完成:', healthReport);
    return healthReport;

  } catch (error) {
    console.error('系統健康檢查錯誤:', error);
    return {
      timestamp: new Date(),
      overall: 'critical_error',
      error: error.toString(),
      modules: {
        core: 'error',
        sheets: 'error',
        memory: 'error',
        groupChat: 'error'
      },
      warnings: [],
      errors: [error.toString()]
    };
  }
}

// 工作表健康檢查函數（統一版本）
function checkSheetsHealth() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = [
      '活動日誌', '摘要日誌', '筆記暫存', '檔案日誌', '地點日誌',
      'APIKEY', 'Threads發文紀錄', '用戶對話歷史', '檔案記憶庫', '分析任務佇列',
      '群組發言記錄' // 🆕 新增群組聊天記錄工作表
    ];

    const healthReport = {
      totalSheets: requiredSheets.length,
      existingSheets: 0,
      missingSheets: [],
      sheetDetails: {}
    };

    requiredSheets.forEach(sheetName => {
      try {
        const sheet = ss.getSheetByName(sheetName);
        if (sheet) {
          healthReport.existingSheets++;
          healthReport.sheetDetails[sheetName] = {
            exists: true,
            rows: sheet.getLastRow(),
            columns: sheet.getLastColumn()
          };
        } else {
          healthReport.missingSheets.push(sheetName);
          healthReport.sheetDetails[sheetName] = {
            exists: false,
            rows: 0,
            columns: 0
          };
        }
      } catch (sheetError) {
        console.error(`檢查工作表 ${sheetName} 錯誤:`, sheetError);
        healthReport.missingSheets.push(sheetName);
        healthReport.sheetDetails[sheetName] = {
          exists: false,
          rows: 0,
          columns: 0,
          error: sheetError.message
        };
      }
    });

    console.log('工作表健康檢查完成:', healthReport);
    return healthReport;
  } catch (error) {
    console.error('工作表健康檢查錯誤:', error);
    return {
      error: error.toString(),
      totalSheets: 0,
      existingSheets: 0,
      missingSheets: [],
      sheetDetails: {}
    };
  }
}

// 記憶系統健康檢查函數（統一版本）
function checkMemorySystemHealth() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const requiredSheets = ['用戶對話歷史', '檔案記憶庫', '分析任務佇列'];
    const result = {
      status: 'healthy',
      sheets: {},
      totalRecords: 0
    };

    let healthySheets = 0;

    requiredSheets.forEach(sheetName => {
      try {
        const sheet = ss.getSheetByName(sheetName);
        if (sheet) {
          const recordCount = Math.max(0, sheet.getLastRow() - 1); // 扣除標題行，確保不為負數
          result.sheets[sheetName] = {
            exists: true,
            records: recordCount
          };
          result.totalRecords += recordCount;
          healthySheets++;
        } else {
          result.sheets[sheetName] = {
            exists: false,
            records: 0
          };
          result.status = 'warning';
        }
      } catch (sheetError) {
        console.error(`檢查記憶系統工作表 ${sheetName} 錯誤:`, sheetError);
        result.sheets[sheetName] = {
          exists: false,
          records: 0,
          error: sheetError.message
        };
        result.status = 'warning';
      }
    });

    // 如果所有工作表都不存在，則為錯誤狀態
    if (healthySheets === 0) {
      result.status = 'error';
    }

    console.log('記憶系統健康檢查完成:', result);
    return result;
  } catch (error) {
    console.error('記憶系統健康檢查錯誤:', error);
    return {
      status: 'error',
      error: error.toString(),
      sheets: {},
      totalRecords: 0
    };
  }
}



/**
 * 🧪 完整系統測試
 * 測試所有模組的核心功能
 */
function runCompleteSystemTest() {
  console.log('🧪 開始完整系統測試...');
  
  const testResults = {
    startTime: new Date(),
    tests: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0
    }
  };
  
  // 測試清單
  const tests = [
    { name: '工作表初始化', func: 'testInitializeAllSheets' },
    { name: 'Webhook 路由', func: 'testWebhookRouting' },
    { name: '命令意圖分析', func: 'testCommandIntentAnalysis' },
    { name: '媒體處理流程', func: 'testMediaProcessing' },
    { name: 'Drive 連結解析', func: 'testDriveLinkParsing' },
    { name: '檔案引用檢測', func: 'testDetectFileReference' },
    { name: '時間範圍解析', func: 'testParseTimeRange' },
    { name: '群組聊天記錄', func: 'testGroupChatTracker' } // 🆕 新增測試
  ];
  
  // 執行測試
  tests.forEach(test => {
    try {
      console.log(`執行測試: ${test.name}`);
      
      // 動態調用測試函數
      if (typeof eval(test.func) === 'function') {
        eval(test.func + '()');
        testResults.tests.push({
          name: test.name,
          status: 'passed',
          message: '測試通過'
        });
        testResults.summary.passed++;
      } else {
        testResults.tests.push({
          name: test.name,
          status: 'skipped',
          message: `測試函數 ${test.func} 不存在`
        });
      }
      
    } catch (error) {
      testResults.tests.push({
        name: test.name,
        status: 'failed',
        message: error.toString()
      });
      testResults.summary.failed++;
    }
    
    testResults.summary.total++;
  });
  
  testResults.endTime = new Date();
  testResults.duration = testResults.endTime - testResults.startTime;
  
  console.log('🧪 完整系統測試完成:', testResults);
  return testResults;
}

/**
 * 📊 生成系統狀態報告
 */
function generateSystemStatusReport() {
  try {
    const healthCheck = systemHealthCheck();
    const config = getConfig();
    
    let report = `📊 LINE Bot 系統狀態報告 (v1.5.0)\n`;
    report += `⏰ 檢查時間：${new Date().toLocaleString('zh-TW')}\n`;
    report += `🎯 整體狀態：${healthCheck.overall === 'healthy' ? '✅ 正常' : '⚠️ 異常'}\n\n`;
    
    report += `🔧 模組狀態：\n`;
    Object.entries(healthCheck.modules).forEach(([module, status]) => {
      const statusIcon = status === 'healthy' ? '✅' : status === 'warning' ? '⚠️' : '❌';
      const moduleNames = {
        core: '核心模組',
        sheets: '工作表系統',
        memory: '記憶系統',
        groupChat: '群組聊天記錄'
      };
      report += `• ${moduleNames[module] || module}: ${statusIcon}\n`;
    });
    
    report += `\n🆕 群組聊天記錄功能：\n`;
    report += `• 自動記錄群組發言：✅ 已實現\n`;
    report += `• 昵稱匹配查詢：✅ 已實現 (蚵仔煎→蚵大、煎大)\n`;
    report += `• AI 發言分析：✅ 已實現\n`;
    report += `• 時間範圍篩選：✅ 已實現\n`;
    
    report += `\n🤖 AI 配置：\n`;
    report += `• Gemini API：${config.geminiApiKey ? '✅ 已設定' : '❌ 未設定'}\n`;
    report += `• Google Search：${config.googleSearchApiKey ? '✅ 已設定' : '❌ 未設定'}\n`;
    report += `• 一般模型：${config.generalModel}\n`;
    report += `• 視覺模型：${config.visionModel}\n`;
    
    report += `\n📱 LINE 配置：\n`;
    report += `• Channel Token：${config.lineChannelAccessToken ? '✅ 已設定' : '❌ 未設定'}\n`;
    report += `• Channel Secret：${config.lineChannelSecret ? '✅ 已設定' : '❌ 未設定'}\n`;
    
    report += `\n💾 儲存配置：\n`;
    report += `• Google Drive：${config.folderId ? '✅ 已設定' : '❌ 未設定'}\n`;
    
    if (healthCheck.warnings.length > 0) {
      report += `\n⚠️ 警告：\n`;
      healthCheck.warnings.forEach(warning => {
        report += `• ${warning}\n`;
      });
    }
    
    if (healthCheck.errors.length > 0) {
      report += `\n❌ 錯誤：\n`;
      healthCheck.errors.forEach(error => {
        report += `• ${error}\n`;
      });
    }
    
    return report;
    
  } catch (error) {
    return `❌ 系統狀態報告生成失敗：${error.message}`;
  }
}

/**
 * 🔄 重新初始化整個系統
 */
function reinitializeSystem() {
  try {
    console.log('🔄 開始重新初始化系統...');
    
    // 1. 初始化工作表
    initializeSheets();
    console.log('✅ 工作表初始化完成');
    
    // 2. 檢查配置
    const config = getConfig();
    console.log('✅ 配置檢查完成');
    
    // 3. 檢查記憶系統
    const memoryHealth = checkMemorySystemHealth();
    console.log('✅ 記憶系統檢查完成');
    
    // 4. 🆕 初始化群組聊天記錄系統
    try {
      const groupSheet = setupGroupChatSheetHeaders();
      console.log('✅ 群組聊天記錄系統初始化完成');
    } catch (error) {
      console.log('⚠️ 群組聊天記錄系統初始化失敗:', error.message);
    }
    
    // 5. 最終健康檢查
    const finalHealth = systemHealthCheck();
    
    const result = {
      success: true,
      timestamp: new Date(),
      message: '系統重新初始化完成 (包含群組聊天記錄功能)',
      healthStatus: finalHealth.overall,
      version: SCRIPT_VERSION
    };
    
    console.log('🔄 系統重新初始化完成:', result);
    return result;
    
  } catch (error) {
    console.error('❌ 系統重新初始化失敗:', error);
    return {
      success: false,
      timestamp: new Date(),
      error: error.toString()
    };
  }
}

// ===== 🧪 測試入口 =====

/**
 * 🧪 快速系統測試
 */
function quickSystemTest() {
  console.log('🧪 執行快速系統測試...');
  
  try {
    // 基本配置測試
    const config = getConfig();
    console.log('✅ 配置讀取正常');
    
    // 工作表測試
    const sheetsHealth = checkSheetsHealth();
    console.log('✅ 工作表狀態正常');
    
    // 記憶系統測試
    const memoryHealth = checkMemorySystemHealth();
    console.log('✅ 記憶系統正常');
    
    // 🆕 群組聊天記錄測試
    try {
      testGroupChatTracker();
      console.log('✅ 群組聊天記錄系統正常');
    } catch (error) {
      console.log('⚠️ 群組聊天記錄系統測試失敗:', error.message);
    }
    
    console.log('🎉 快速系統測試通過！v1.5.0 群組聊天記錄功能已就緒');
    return true;
    
  } catch (error) {
    console.error('❌ 快速系統測試失敗:', error);
    return false;
  }
}

// ===== 管理員工具 =====

/**
 * 🔧 管理員：清理所有舊數據
 */
function adminCleanupOldData() {
  try {
    console.log('🔧 管理員工具：開始清理舊數據...');
    
    // 清理工作表
    const cleanupResult = cleanupWorksheets();
    console.log('工作表清理結果:', cleanupResult);
    
    // 清理 Drive 檔案快取
    const driveCleanup = manageDriveFileCache();
    console.log('Drive 快取清理結果:', driveCleanup);
    
    return `✅ 數據清理完成\n• ${cleanupResult}\n• ${driveCleanup.message || driveCleanup.error}`;
    
  } catch (error) {
    console.error('數據清理錯誤:', error);
    return `❌ 數據清理失敗：${error.message}`;
  }
}

/**
 * 📈 管理員：生成使用統計報告
 */
function adminGenerateUsageReport() {
  try {
    const report = {
      timestamp: new Date(),
      version: SCRIPT_VERSION,
      sheets: checkSheetsHealth(),
      memory: checkMemorySystemHealth(),
      drive: generateDriveFileReport('all'),
      media: generateMediaProcessingReport('all'),
      groupChat: { // 🆕 群組聊天統計
        enabled: true,
        implementation: 'complete'
      }
    };
    
    console.log('📈 使用統計報告 v1.1.5:', report);
    return report;
    
  } catch (error) {
    console.error('生成統計報告錯誤:', error);
    return { error: error.toString() };
  }
}

// ===== 版本資訊 =====
const SYSTEM_VERSION = {
  version: SCRIPT_VERSION,
  architecture: 'Modular',
  lastUpdate: '2025-01-01',
  features: [
    '群組聊天記錄與查詢', // 🆕
    'AI 智能回答',
    '檔案分析與記憶',
    '媒體處理',
    'Google Drive 整合',
    '多語言指令支援',
    '記憶系統'
  ],
  modules: [
    'Core.gs',
    'Sheets.gs', 
    'Webhook.gs',
    'TextProcessor.gs',
    'MediaProcessor.gs',
    'DriveHandler.gs',
    'Memory_System.gs',
    'AI_Features.gs',
    'File_Analysis.gs',
    'GroupChatTracker.gs' // 🆕
  ]
};

/**
 * 獲取版本資訊
 */
function getVersionInfo() {
  return {
    version: SCRIPT_VERSION,
    system: SYSTEM_VERSION
  };
}

console.log('🚀 LINE Bot 模組化系統已載入', SYSTEM_VERSION);
